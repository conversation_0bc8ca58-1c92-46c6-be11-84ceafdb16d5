/* Component Styles */

/* Container Styles */
.container {
  width: 100%;
  max-width: 500px; /* Adequate size for timer circle */
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-xl) var(--spacing-2xl);
  text-align: center;
  position: relative;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
}

.header-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* Now Playing */
.now-playing {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 500px;
  overflow: hidden;
  white-space: nowrap;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.now-playing-content {
  display: inline-flex;
  font-weight: 500;
  white-space: nowrap;
  animation: seamless-scroll 20s linear infinite;
}

.now-playing-text {
  display: inline-block;
  white-space: nowrap;
  margin-right: 100px; /* Gap between duplicate texts */
}

@keyframes seamless-scroll {
  0% {
    transform: translateX(0%);
  }
  25% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.settings-btn,
.report-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  color: var(--primary-color);
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  aspect-ratio: 1;
}

.settings-btn svg,
.report-btn svg {
  width: 24px;
  height: 24px;
  transition: all var(--transition-fast);
}

.settings-btn:hover {
  background: var(--bg-secondary);
}

.settings-btn:hover svg {
  transform: rotate(90deg);
}

.settings-btn:active {
  transform: scale(0.95);
}

.settings-btn:active svg {
  transform: rotate(90deg) scale(0.95);
}

.report-btn:hover {
  background: var(--bg-secondary);
}

.report-btn:hover svg {
  transform: scale(1.1);
}

.report-btn:active {
  transform: scale(0.95);
}

/* Main Content */
.main-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* Timer Display */
.timer-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.session-info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 704px;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.session-type {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.session-counter {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  font-weight: 500;
}

/* Timer Circle */
.timer-circle {
  position: relative;
  width: min(320px, 80vw);
  height: min(320px, 80vw);
  max-width: 400px;
  max-height: 400px;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--spacing-lg) auto;
  border-radius: 50%;
  background: transparent;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  /* Improve rendering quality */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Force hardware acceleration */
  transform: translateZ(0);
  will-change: transform;
  /* Improve edge rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Timer Circle Background Ring */
.timer-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 8px solid var(--border-color);
  box-sizing: border-box;
  /* Anti-aliasing for smoother edges */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Rounded line caps for smoother appearance */
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* Timer Circle Progress Ring */
.timer-circle::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from var(--progress-start-angle, 0deg),
    var(--primary-color) 0deg,
    var(--primary-color) var(--progress-degrees, 0deg),
    transparent var(--progress-degrees, 0deg),
    transparent 360deg
  );
  /* Smoother mask with gradual transitions and better anti-aliasing */
  mask: radial-gradient(circle, transparent 149px, rgba(0,0,0,0.1) 151px, black 153px, black 159px, rgba(0,0,0,0.9) 161px, transparent 163px);
  -webkit-mask: radial-gradient(circle, transparent 149px, rgba(0,0,0,0.1) 151px, black 153px, black 159px, rgba(0,0,0,0.9) 161px, transparent 163px);
  transition: background var(--transition-normal) ease-in-out;
  transform: var(--progress-direction, scaleX(1));
  /* Anti-aliasing and smoothing */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  will-change: background;
  /* Force hardware acceleration for smoother rendering */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* Additional smoothing for better edge quality */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  /* Optimize for smooth animations */
  -webkit-perspective: 1000;
  perspective: 1000;
  -webkit-backface-visibility: hidden;
  /* Rounded caps effect using filter */
  filter: blur(0.5px);
}

/* Rounded caps for progress ring */
.timer-circle {
  position: relative;
}

.timer-circle::before,
.timer-circle::after {
  border-radius: 50%;
}

/* Add rounded end caps */
.timer-circle .progress-cap-start,
.timer-circle .progress-cap-end {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform-origin: center;
  opacity: var(--cap-opacity, 0);
  transition: opacity var(--transition-normal) ease-in-out;
  z-index: 2;
}

.timer-circle .progress-cap-start {
  transform: translate(-50%, -50%) translateY(-156px);
}

.timer-circle .progress-cap-end {
  transform: translate(-50%, -50%) translateY(-156px) rotate(var(--progress-degrees, 0deg));
}

/* Timer Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}



.timer-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.timer-minutes,
.timer-seconds {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
  line-height: 1;
  min-width: 1.2em;
  text-align: center;
}

.timer-separator {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-secondary);
  animation: pulse 1s infinite;
}

/* Session Progress Dots - Hidden */
.session-dots {
  display: none;
}

.session-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
}

.session-dot.completed {
  background: var(--success-color);
  transform: scale(1.2);
}

.session-dot.current {
  background: var(--primary-color);
  transform: scale(1.3);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.3);
}

/* Timer Controls */
.timer-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin: var(--spacing-xl) 0;
}

.control-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 120px;
  position: relative;
  overflow: hidden;
}

.control-btn.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.control-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.control-btn.primary:active {
  transform: translateY(0);
}

.control-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.control-btn.secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.control-btn.danger {
  background: #ef4444;
  color: white;
  border: 2px solid #dc2626;
}

.control-btn.danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 2px solid var(--border-color);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  transition: all var(--transition-normal);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  font-variant-numeric: tabular-nums;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal.hidden {
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.report-section {
  margin-bottom: var(--spacing-xl);
}

.report-section h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: var(--spacing-xs);
}

.report-section h4 {
  margin: var(--spacing-md) 0 var(--spacing-sm) 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.date-selector-container {
  margin-bottom: var(--spacing-lg);
}

.date-selector-container label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.date-selector-container select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

/* Horizontal layout for stats */
.report-stats-horizontal, .daily-stats-horizontal {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.report-stats-horizontal .stat-item, .daily-stats-horizontal .stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.report-stats-horizontal .stat-label, .daily-stats-horizontal .stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 500;
}

.report-stats-horizontal .stat-value, .daily-stats-horizontal .stat-value {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
}

/* Legacy vertical layout (if needed) */
.report-stats, .daily-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.report-stats .stat-item, .daily-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.report-stats .stat-label, .daily-stats .stat-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
}

.report-stats .stat-value, .daily-stats .stat-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

/* Weekly Chart Container */
.weekly-chart-container {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.weekly-chart-container h4 {
  margin-bottom: var(--spacing-lg);
}

.weekly-chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 200px;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  gap: var(--spacing-sm);
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.chart-bar-container {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chart-bar-fill {
  width: 60%;
  background: linear-gradient(to top, var(--primary-color), var(--primary-light));
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  min-height: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.chart-bar-fill:hover {
  background: linear-gradient(to top, var(--primary-dark), var(--primary-color));
  transform: scaleY(1.05);
}

.chart-bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--text-primary);
  background: var(--bg-primary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-bar-fill:hover .chart-bar-value {
  opacity: 1;
}

.chart-bar-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
}

.chart-bar-day {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

/* Empty state for chart */
.chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
}

/* Legacy sessions styles (kept for compatibility) */
.sessions-container {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-md);
}

.sessions-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
}

.session-item {
  margin-bottom: var(--spacing-xs);
}

.session-item:last-child {
  margin-bottom: 0;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.session-type {
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.session-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-family: monospace;
}

.session-details {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Development Note */
.development-note {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: var(--border-radius);
  text-align: center;
}

.development-note p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: rgb(67, 56, 202);
  line-height: 1.5;
}

.development-note strong {
  font-weight: 600;
}
