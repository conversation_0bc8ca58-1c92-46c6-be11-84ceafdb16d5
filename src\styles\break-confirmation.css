/* Break Confirmation Styles */
.break-content {
  text-align: center;
  padding: var(--spacing-xl);
}

.break-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 var(--spacing-lg) 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.break-message {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xl) 0;
  line-height: 1.6;
}

.break-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  margin: var(--spacing-xl) 0;
  border: 2px solid var(--border-color);
}

.break-duration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.break-duration-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.break-duration-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
}

.bonus-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.bonus-time-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bonus-time-value {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--warning-color);
}

.break-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.break-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 140px;
}

.break-btn.primary {
  background: linear-gradient(135deg, var(--success-color), #22c55e);
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.break-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.break-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.break-btn.secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

/* Grace Period Indicator */
.grace-period {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--warning-color);
  color: var(--text-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 600;
  animation: pulse 1s infinite;
}

/* Break Animation */
.break-content.entering {
  animation: slideUp var(--transition-slow) ease-out;
}

.break-content.leaving {
  animation: fadeOut var(--transition-normal) ease-in;
}
